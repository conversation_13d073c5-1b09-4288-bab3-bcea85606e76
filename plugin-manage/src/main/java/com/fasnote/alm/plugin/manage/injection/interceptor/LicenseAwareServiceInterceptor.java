package com.fasnote.alm.plugin.manage.injection.interceptor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.annotations.Service;
import com.fasnote.alm.injection.api.IInjectionContext;
import com.fasnote.alm.injection.api.IServiceInterceptor;
import com.fasnote.alm.plugin.manage.annotation.FallbackImplementation;
import com.fasnote.alm.plugin.manage.annotation.LicenseImplementation;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.injection.processor.LicenseAnnotationProcessor;

/**
 * 许可证感知的服务拦截器
 * 
 * 负责根据许可证状态和优先级选择合适的服务实现：
 * 1. 许可证验证通过时，优先使用 @LicenseImplementation 标记的实现
 * 2. 许可证验证失败时，使用 @FallbackImplementation 标记的实现
 * 3. 支持优先级排序和命名服务
 */
public class LicenseAwareServiceInterceptor implements IServiceInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(LicenseAwareServiceInterceptor.class);
    
    // 缓存接口的所有实现类信息
    private final Map<Class<?>, List<ServiceImplementationInfo>> implementationCache = new ConcurrentHashMap<>();
    
    private LicenseManager licenseManager;

    public LicenseAwareServiceInterceptor(LicenseManager licenseManager) {
        this.licenseManager = licenseManager;
    }
    
    @Override
    public boolean shouldIntercept(Class<?> serviceClass) {
        // 检查是否有许可证相关注解的实现类
        return hasLicenseRelatedImplementations(serviceClass);
    }
    
    @Override
    public Object beforeCreate(Class<?> serviceClass, IInjectionContext context) {
        try {
            // 获取接口的所有实现
            List<ServiceImplementationInfo> implementations = getImplementations(serviceClass);
            if (implementations.isEmpty()) {
                return null; // 让 DI 容器使用默认逻辑
            }
            
            // 根据许可证状态选择合适的实现
            Class<?> selectedImplementation = selectImplementationByLicense(serviceClass, implementations);
            if (selectedImplementation != null) {
                logger.debug("许可证感知服务选择: " + serviceClass.getName() + " -> " + selectedImplementation.getName());

                // 通过反射创建选定的实现实例
                return selectedImplementation.getDeclaredConstructor().newInstance();
            }
            
        } catch (Exception e) {
            logger.error("许可证感知服务选择失败: " + serviceClass.getName(), e);
        }
        
        return null; // 让 DI 容器使用默认逻辑
    }
    
    @Override
    public int getPriority() {
        return 10; // 高优先级，确保在其他拦截器之前执行
    }
    
    /**
     * 检查接口是否有许可证相关的实现类
     */
    private boolean hasLicenseRelatedImplementations(Class<?> serviceInterface) {
        // 这里可以通过扫描或缓存来检查
        // 简化实现：检查缓存中是否有该接口的实现信息
        return implementationCache.containsKey(serviceInterface) || 
               scanForImplementations(serviceInterface);
    }
    
    /**
     * 扫描接口的实现类
     */
    private boolean scanForImplementations(Class<?> serviceInterface) {
        // 这个方法可以与现有的包扫描机制集成
        // 或者通过 DI 容器的服务注册信息来获取
        // 这里先返回 false，具体实现可以后续完善
        return false;
    }
    
    /**
     * 获取接口的所有实现类信息
     */
    private List<ServiceImplementationInfo> getImplementations(Class<?> serviceInterface) {
        return implementationCache.computeIfAbsent(serviceInterface, this::loadImplementations);
    }
    
    /**
     * 加载接口的实现类信息
     */
    private List<ServiceImplementationInfo> loadImplementations(Class<?> serviceInterface) {
        List<ServiceImplementationInfo> implementations = new ArrayList<>();
        
        // 这里需要与 DI 容器集成，获取所有注册的实现类
        // 可以通过 IInjectionContext 或其他方式获取
        // 暂时返回空列表，具体实现需要与 DI 容器的 API 配合
        
        return implementations;
    }
    
    /**
     * 根据许可证状态选择合适的实现
     */
    private Class<?> selectImplementationByLicense(Class<?> serviceInterface, List<ServiceImplementationInfo> implementations) {
        // 按优先级排序
        implementations.sort((a, b) -> Integer.compare(a.getPriority(), b.getPriority()));
        
        // 首先尝试许可证实现
        for (ServiceImplementationInfo impl : implementations) {
            if (impl.isLicenseImplementation()) {
                // 检查许可证是否有效
                if (licenseManager != null && isLicenseValid(serviceInterface, impl.getImplementationClass())) {
                    logger.debug("选择许可证实现: " + impl.getImplementationClass().getName() + " (优先级: " + impl.getPriority() + ")");
                    return impl.getImplementationClass();
                }
            }
        }
        
        // 如果没有有效的许可证实现，选择回退实现
        for (ServiceImplementationInfo impl : implementations) {
            if (impl.isFallbackImplementation()) {
                logger.debug("选择回退实现: " + impl.getImplementationClass().getName() + " (优先级: " + impl.getPriority() + ")");
                return impl.getImplementationClass();
            }
        }
        
        // 如果都没有，返回第一个实现
        if (!implementations.isEmpty()) {
            ServiceImplementationInfo firstImpl = implementations.get(0);
            logger.debug("选择默认实现: " + firstImpl.getImplementationClass().getName() + " (优先级: " + firstImpl.getPriority() + ")");
            return firstImpl.getImplementationClass();
        }
        
        return null;
    }
    
    /**
     * 检查许可证是否有效
     */
    private boolean isLicenseValid(Class<?> serviceInterface, Class<?> implementationClass) {
        try {
            // 使用现有的许可证管理器进行验证
            Object instance = licenseManager.createServiceInstanceFromLicense(serviceInterface);
            return instance != null && implementationClass.isInstance(instance);
        } catch (Exception e) {
            logger.debug("许可证验证失败: " + implementationClass.getName(), e);
            return false;
        }
    }
    
    /**
     * 注册实现类信息到缓存
     * 这个方法可以被 DI 容器在服务注册时调用
     */
    public void registerImplementation(Class<?> serviceInterface, Class<?> implementationClass) {
        ServiceImplementationInfo info = createImplementationInfo(implementationClass);
        if (info != null) {
            implementationCache.computeIfAbsent(serviceInterface, k -> new ArrayList<>()).add(info);
            logger.debug("注册实现类信息: {} -> {}", serviceInterface.getName(), implementationClass.getName());
        }
    }
    
    /**
     * 创建实现类信息
     */
    private ServiceImplementationInfo createImplementationInfo(Class<?> implementationClass) {
        // 检查是否有许可证相关注解
        if (!LicenseAnnotationProcessor.hasLicenseAnnotation(implementationClass)) {
            // 检查是否有直接的 @Service 注解
            Service serviceAnnotation = implementationClass.getAnnotation(Service.class);
            if (serviceAnnotation == null) {
                return null; // 不是服务类
            }

            // 创建普通服务信息
            return new ServiceImplementationInfo(implementationClass, serviceAnnotation.priority(), false, false);
        }

        // 使用注解处理器提取配置
        LicenseAnnotationProcessor.ServiceConfig config = LicenseAnnotationProcessor.extractServiceConfig(implementationClass);
        if (config == null) {
            return null;
        }

        return new ServiceImplementationInfo(
            config.getImplementationClass(),
            config.getPriority(),
            config.isLicenseImplementation(),
            config.isFallbackImplementation()
        );
    }
    
    /**
     * 服务实现信息
     */
    private static class ServiceImplementationInfo {
        private final Class<?> implementationClass;
        private final int priority;
        private final boolean isLicenseImplementation;
        private final boolean isFallbackImplementation;
        
        public ServiceImplementationInfo(Class<?> implementationClass, int priority, 
                                       boolean isLicenseImplementation, boolean isFallbackImplementation) {
            this.implementationClass = implementationClass;
            this.priority = priority;
            this.isLicenseImplementation = isLicenseImplementation;
            this.isFallbackImplementation = isFallbackImplementation;
        }
        
        public Class<?> getImplementationClass() { return implementationClass; }
        public int getPriority() { return priority; }
        public boolean isLicenseImplementation() { return isLicenseImplementation; }
        public boolean isFallbackImplementation() { return isFallbackImplementation; }
    }
}
