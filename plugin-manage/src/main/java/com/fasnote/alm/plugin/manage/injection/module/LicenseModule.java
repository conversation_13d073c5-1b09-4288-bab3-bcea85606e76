package com.fasnote.alm.plugin.manage.injection.module;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IBinder;
import com.fasnote.alm.injection.api.IInjectionContext;
import com.fasnote.alm.injection.api.IModule;
import com.fasnote.alm.injection.api.IServiceInterceptor;
import com.fasnote.alm.injection.api.IServiceProvider;
import com.fasnote.alm.plugin.manage.api.ILicenseBusinessRegistry;
import com.fasnote.alm.plugin.manage.api.IPackageScanProvider;
import com.fasnote.alm.plugin.manage.api.LicenseAware;
import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.injection.LicenseBusinessRegistry;
import com.fasnote.alm.plugin.manage.injection.LicenseValidationEnhancedInterceptor;
import com.fasnote.alm.plugin.manage.injection.PackageScanManager;
import com.fasnote.alm.plugin.manage.injection.PackageScanManager.PackageScanProviderChangeListener;
import com.fasnote.alm.plugin.manage.injection.interceptor.LicenseAwareServiceInterceptor;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.security.ISecurityValidator;
import com.fasnote.alm.plugin.manage.security.SecurityValidationInterceptor;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;

/**
 * 许可证模块 通过DI框架的扩展机制提供许可证相关功能 负责从许可证文件中解密和注册业务服务实现
 */
public class LicenseModule implements IModule, PackageScanProviderChangeListener {

	/**
	 * 许可证感知服务提供者
	 */
	private class LicenseAwareProvider implements IServiceProvider<LicenseAware> {

		@Override
		public LicenseAware provide(IInjectionContext context) {
			// 返回一个简单的许可证信息访问器
			return new LicenseAware() {
				private PluginLicense license;

				@Override
				public PluginLicense getLicenseInfo() {
					return license;
				}

				@Override
				public void setLicenseInfo(PluginLicense license) {
					this.license = license;
				}
			};
		}
	}

	/**
	 * 许可证服务拦截器 创建动态代理，在运行时根据许可证状态选择实现
	 */
	private class LicenseServiceInterceptor implements IServiceInterceptor {

		@Override
		public Object afterCreate(Class<?> serviceClass, Object instance, IInjectionContext context) {
			// 如果实例实现了LicenseAware接口，注入许可证信息
			if (instance instanceof LicenseAware) {
				Class<?> serviceInterface = findLicenseableInterface(serviceClass);
				if (serviceInterface != null) {
					injectLicenseInfo((LicenseAware) instance, serviceInterface);
				}
			}
			return instance;
		}

		@Override
		public Object beforeCreate(Class<?> serviceClass, IInjectionContext context) {
			// 主要逻辑已移到服务注册阶段，这里只做简单检查
			return null;
		}

		/**
		 * 找到实现类对应的可许可接口
		 */
		private Class<?> findLicenseableInterface(Class<?> clazz) {
			// 如果本身就是接口，直接返回
			if (clazz.isInterface()) {
				return clazz;
			}

			// 查找实现的接口
			Class<?>[] interfaces = clazz.getInterfaces();
			for (Class<?> intf : interfaces) {
				String packageName = intf.getPackage().getName();
				if (!packageName.startsWith("java.") && !packageName.startsWith("javax.")
						&& !packageName.startsWith("org.osgi.") && !packageName.startsWith("org.slf4j.")) {
					return intf;
				}
			}
			return null;
		}

		@Override
		public int getPriority() {
			return 5; // 高优先级
		}

		@Override
		public boolean shouldIntercept(Class<?> serviceClass) {
			// 排除系统包
			if (serviceClass.getPackage() != null) {
				String packageName = serviceClass.getPackage().getName();
				if (packageName.startsWith("java.") || packageName.startsWith("javax.")
						|| packageName.startsWith("org.osgi.") || packageName.startsWith("org.slf4j.")) {
					return false;
				}
			}

			// 检查是否有对应的许可证接口
			Class<?> licenseableInterface = findLicenseableInterface(serviceClass);
			boolean shouldIntercept = licenseableInterface != null;

			logger.debug("许可证拦截器检查: {} -> {} (接口: {})", serviceClass.getName(), shouldIntercept,
					licenseableInterface != null ? licenseableInterface.getName() : "无");
			return shouldIntercept;
		}
	}
	private static final Logger logger = LoggerFactory.getLogger(LicenseModule.class);
	private final LicenseManager licenseManager;

	private final ILicenseBusinessRegistry businessRegistry;

	private final PackageScanManager packageScanManager;

	// 保存 IBinder 引用，用于动态注册
	private IBinder currentBinder;

	/**
	 * 依赖注入构造函数
	 *
	 * @param licenseManager 许可证管理器（由DI框架注入）
	 */
	public LicenseModule(LicenseManager licenseManager) {
		if (licenseManager == null) {
			throw new IllegalArgumentException("LicenseManager不能为null");
		}
		this.licenseManager = licenseManager;
		this.businessRegistry = new LicenseBusinessRegistry();
		this.packageScanManager = new PackageScanManager(getBundleContext());
		logger.info("LicenseModule通过依赖注入创建");
	}

	@Override
	public void configure(IBinder binder) {
		logger.info("配置LicenseModule...");

		// 保存 IBinder 引用，用于动态注册
		this.currentBinder = binder;

		// 设置包扫描管理器的变更监听器
		//packageScanManager.setChangeListener(this);

		// 注解驱动的业务实现注册
		//registerBusinessImplementations(binder);

		// 动态注册许可证服务实现
		//registerLicenseServices(binder);

		// 注册许可证感知服务拦截器（新架构）
		LicenseAwareServiceInterceptor licenseAwareInterceptor = new LicenseAwareServiceInterceptor(licenseManager);
		binder.registerInterceptor(licenseAwareInterceptor);
		logger.info("许可证感知服务拦截器已注册，优先级: " + licenseAwareInterceptor.getPriority());

		// 注册许可证服务拦截器（保持向后兼容）
		LicenseServiceInterceptor licenseInterceptor = new LicenseServiceInterceptor();
		binder.registerInterceptor(licenseInterceptor);
		logger.info("许可证服务拦截器已注册，优先级: " + licenseInterceptor.getPriority());

		// 注册安全验证拦截器
		SecurityValidationInterceptor securityInterceptor = new SecurityValidationInterceptor();
		// 尝试获取SecurityValidator实例并设置到拦截器中
		try {
			ISecurityValidator securityValidator = getSecurityValidator();
			if (securityValidator != null) {
				securityInterceptor.setSecurityValidator(securityValidator);
				logger.debug("SecurityValidator已设置到安全验证拦截器");
			} else {
				logger.warn("SecurityValidator未找到，安全验证拦截器将无法正常工作");
			}
		} catch (Exception e) {
			logger.warn("获取SecurityValidator失败", e);
		}
		binder.registerInterceptor(securityInterceptor);

		// 注册增强的许可证验证拦截器（支持方法级别AOP）
		try {
			SecurityValidator concreteSecurityValidator = getConcreteSecurityValidator();
			if (concreteSecurityValidator != null) {
				LicenseValidationEnhancedInterceptor enhancedInterceptor = new LicenseValidationEnhancedInterceptor(
						licenseManager, concreteSecurityValidator);
				binder.registerInterceptor(enhancedInterceptor);
				logger.info("增强许可证验证拦截器已注册（支持方法级别AOP）");
			} else {
				logger.warn("无法创建增强许可证验证拦截器：SecurityValidator未找到");
			}
		} catch (Exception e) {
			logger.warn("注册增强许可证验证拦截器失败", e);
		}

		// 注册许可证感知服务提供者
		binder.bind(LicenseAware.class).toProvider(new LicenseAwareProvider()).build();

		logger.info("LicenseModule配置完成");
	}

	/**
	 * 获取 BundleContext
	 *
	 * @return BundleContext 实例，如果无法获取则返回 null
	 */
	private org.osgi.framework.BundleContext getBundleContext() {
		try {
			// 尝试从 LicenseManager 获取 BundleContext
			if (licenseManager != null) {
				// 通过反射获取 LicenseManager 的 bundleContext 字段
				java.lang.reflect.Field bundleContextField = licenseManager.getClass()
						.getDeclaredField("bundleContext");
				bundleContextField.setAccessible(true);
				Object bundleContext = bundleContextField.get(licenseManager);
				if (bundleContext instanceof org.osgi.framework.BundleContext) {
					logger.debug("从 LicenseManager 获取到 BundleContext");
					return (org.osgi.framework.BundleContext) bundleContext;
				}
			}
		} catch (Exception e) {
			logger.debug("无法从 LicenseManager 获取 BundleContext", e);
		}

		try {
			// 尝试从 Activator 获取 BundleContext
			Class<?> activatorClass = Class.forName("com.fasnote.alm.plugin.manage.Activator");
			java.lang.reflect.Method getContextMethod = activatorClass.getDeclaredMethod("getContext");
			getContextMethod.setAccessible(true);
			Object bundleContext = getContextMethod.invoke(null);
			if (bundleContext instanceof org.osgi.framework.BundleContext) {
				logger.debug("从 Activator 获取到 BundleContext");
				return (org.osgi.framework.BundleContext) bundleContext;
			}
		} catch (Exception e) {
			logger.debug("无法从 Activator 获取 BundleContext", e);
		}

		logger.warn("无法获取 BundleContext，将使用 ServiceLoader 兼容模式");
		return null;
	}

	/**
	 * 获取具体的SecurityValidator实例 用于创建增强拦截器
	 */
	private SecurityValidator getConcreteSecurityValidator() {
		try {
			// 首先尝试从许可证中获取
			ISecurityValidator interfaceValidator = getSecurityValidator();
			if (interfaceValidator instanceof SecurityValidator) {
				return (SecurityValidator) interfaceValidator;
			}

			// 如果许可证中没有，创建默认的SecurityValidator实例
			SecurityValidator defaultValidator = new SecurityValidator();
			// 这里可能需要设置一些默认配置
			return defaultValidator;

		} catch (Exception e) {
			logger.debug("获取具体SecurityValidator失败", e);
			return null;
		}
	}

	@Override
	public String getName() {
		return "LicenseModule";
	}

	@Override
	public int getPriority() {
		return 10; // 高优先级，确保许可证检查在其他模块之前
	}

	/**
	 * 获取SecurityValidator实例 尝试从许可证中创建SecurityValidator实例
	 */
	private ISecurityValidator getSecurityValidator() {
		try {
			// 尝试从许可证中创建SecurityValidator
			Object securityValidator = licenseManager.createServiceInstanceFromLicense(ISecurityValidator.class);
			if (securityValidator instanceof ISecurityValidator) {
				ISecurityValidator validator = (ISecurityValidator) securityValidator;
				// 初始化验证器
				validator.initialize();
				return validator;
			}
		} catch (Exception e) {
			logger.debug("从许可证创建SecurityValidator失败", e);
		}

		return null;
	}

	/**
	 * 为LicenseAware实例注入许可证信息
	 */
	private void injectLicenseInfo(LicenseAware instance, Class<?> serviceClass) {
		try {
			// 尝试找到相关的许可证
			for (String pluginId : licenseManager.getRegisteredPluginIds()) {
				Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
				if (licenseOpt.isPresent()) {
					PluginLicense license = licenseOpt.get();
					if (license.isValid()) {
						// 检查是否包含此服务的映射
						if (license.hasServiceMappings()
								&& license.getServiceMappings().containsKey(serviceClass.getName())) {
							instance.setLicenseInfo(license);
							break;
						}
					}
				}
			}
		} catch (Exception e) {
			logger.warn("注入许可证信息失败: {}", serviceClass.getName(), e);
		}
	}

	/**
	 * 加载接口类，优先使用加密类加载器
	 *
	 * @param pluginId      插件ID
	 * @param interfaceName 接口名称
	 * @return 接口类，如果加载失败返回null
	 */
	private Class<?> loadInterfaceClass(String pluginId, String interfaceName) {
		try {
			// 首先尝试使用插件的加密类加载器
			Optional<EncryptedClassLoader> classLoaderOpt = licenseManager
					.createEncryptedClassLoaderForPlugin(pluginId);
			if (classLoaderOpt.isPresent()) {
				EncryptedClassLoader encryptedClassLoader = classLoaderOpt.get();
				try {
					Class<?> interfaceClass = encryptedClassLoader.loadClass(interfaceName);
					logger.debug("使用加密类加载器成功加载接口类: {} (插件: {})", interfaceName, pluginId);
					return interfaceClass;
				} catch (ClassNotFoundException e) {
					logger.debug("加密类加载器中未找到接口类: {} (插件: {})", interfaceName, pluginId);
				}
			}

			// 如果加密类加载器中没有，尝试使用系统类加载器
			try {
				Class<?> interfaceClass = Class.forName(interfaceName);
				logger.debug("使用系统类加载器成功加载接口类: {} (插件: {})", interfaceName, pluginId);
				return interfaceClass;
			} catch (ClassNotFoundException e) {
				logger.debug("系统类加载器中未找到接口类: {} (插件: {})", interfaceName, pluginId);
			}

			return null;

		} catch (Exception e) {
			logger.warn("加载接口类时发生异常: {} (插件: {})", interfaceName, pluginId, e);
			return null;
		}
	}

	@Override
	public void onProviderAdded(IPackageScanProvider provider) {
		logger.info("检测到新的包扫描提供者: " + provider.getName() + " (" + provider.getPluginId() + ")");

		try {
			// 扫描新提供者的包路径
			String[] packages = provider.getScanPackages();
			if (packages != null && packages.length > 0) {
				logger.info("开始扫描新提供者的包路径: " + Arrays.toString(packages));

				// 测试类加载器是否能找到飞书的类
				testClassLoading(packages[0]);

				// 延迟一小段时间，确保 Bundle 完全启动
				try {
					Thread.sleep(100);
				} catch (InterruptedException e) {
					Thread.currentThread().interrupt();
				}

				// 使用 LicenseBusinessRegistry 扫描新包
				businessRegistry.scanAndRegister(packages);

				// 如果有 IBinder 引用，立即注册到 DI 容器
				if (currentBinder != null) {
					registerImplementationsToDI(currentBinder);
					logger.info("新扫描的实现已注册到 DI 容器");
				} else {
					logger.warn("IBinder 引用为空，无法立即注册新扫描的实现");
				}

				ILicenseBusinessRegistry.RegistrationStats stats = businessRegistry.getRegistrationStats();
				logger.info("新提供者扫描完成，当前统计: 许可证实现=" + stats.getLicenseImplementationCount() + ", 回退实现="
						+ stats.getFallbackImplementationCount() + ", 命名许可证实现="
						+ stats.getNamedLicenseImplementationCount() + ", 命名回退实现="
						+ stats.getNamedFallbackImplementationCount() + ", 总计=" + stats.getTotalCount());
			}
		} catch (Exception e) {
			logger.error("处理新包扫描提供者时发生异常: " + provider.getName(), e);
		}
	}

	@Override
	public void onProviderRemoved(IPackageScanProvider provider) {
		logger.info("包扫描提供者已移除: " + provider.getName() + " (" + provider.getPluginId() + ")");
		// 注意：这里不做实际的注销操作，因为 DI 容器通常不支持动态注销
		// 如果需要支持动态注销，需要在 DI 容器层面实现
	}

	/**
	 * 基于接口名称提供服务 许可证验证系统的核心方法，支持动态服务查找
	 *
	 * @param interfaceName 接口全限定名称
	 * @param context       注入上下文
	 * @return 服务实例，如果未找到返回null
	 */
	public Object provideServiceByInterfaceName(String interfaceName, IInjectionContext context) {
		if (licenseManager == null) {
			logger.debug("LicenseManager未初始化");
			return null;
		}

		logger.debug("基于接口名称查找许可证服务: {}", interfaceName);

		try {
			// 尝试通过接口名称加载接口类
			Class<?> interfaceClass = Class.forName(interfaceName);

			// 使用LicenseManager创建服务实例
			Object instance = licenseManager.createServiceInstanceFromLicense(interfaceClass);
			if (instance != null) {
				logger.info("成功通过接口名称创建服务实例: {}", interfaceName);
				return instance;
			}
		} catch (ClassNotFoundException e) {
			logger.warn("无法加载接口类: {}", interfaceName, e);
		} catch (Exception e) {
			logger.error("通过接口名称创建服务实例失败: {}", interfaceName, e);
		}

		logger.debug("未找到匹配的许可证服务: {}", interfaceName);
		return null;
	}

	/**
	 * 注册业务实现（注解驱动方式）
	 */
	private void registerBusinessImplementations(IBinder binder) {
		logger.info("开始注册业务实现（注解驱动）...");

		try {
			// 启动包扫描管理器
			packageScanManager.start();

			// 从 OSGi 服务获取扫描包路径
			String[] businessPackages = packageScanManager.getAllScanPackages();

			if (businessPackages.length == 0) {
				logger.info("当前未找到包扫描提供者，等待 OSGi 服务注册");
				logger.info("将通过事件机制自动扫描新注册的包扫描提供者");
				return;
			}

			// 使用 LicenseBusinessRegistry 进行自动扫描和注册
			businessRegistry.scanAndRegister(businessPackages);

			// 将扫描到的实现注册到 DI 容器
			registerImplementationsToDI(binder);

			ILicenseBusinessRegistry.RegistrationStats stats = businessRegistry.getRegistrationStats();
			logger.info("业务实现注册完成，统计信息: 许可证实现=" + stats.getLicenseImplementationCount() + ", 回退实现="
					+ stats.getFallbackImplementationCount() + ", 命名许可证实现=" + stats.getNamedLicenseImplementationCount()
					+ ", 命名回退实现=" + stats.getNamedFallbackImplementationCount() + ", 总计=" + stats.getTotalCount());
			logger.info("包扫描提供者信息: " + packageScanManager.getProviderInfo());

		} catch (Exception e) {
			logger.error("注册业务实现时发生异常", e);
		}
	}

	/**
	 * 将 LicenseBusinessRegistry 中的实现注册到 DI 容器
	 */
	@SuppressWarnings("unchecked")
	private void registerImplementationsToDI(IBinder binder) {
		// 注册许可证实现
		Map<Class<?>, Class<?>> licenseImpls = businessRegistry.getLicenseImplementationMappings();
		for (Map.Entry<Class<?>, Class<?>> entry : licenseImpls.entrySet()) {
			Class<?> serviceInterface = entry.getKey();
			Class<?> implementationClass = entry.getValue();

			try {
				binder.registerImplementation((Class<Object>) serviceInterface, (Class<Object>) implementationClass,
						null, // 无名称
						true // 设为主要实现
				);

				logger.debug("注册许可证实现到DI: " + serviceInterface.getName() + " -> " + implementationClass.getName());
			} catch (Exception e) {
				logger.error("注册许可证实现失败: " + serviceInterface.getName() + " -> " + implementationClass.getName(), e);
			}
		}

		// 注册回退实现
		Map<Class<?>, Class<?>> fallbackImpls = businessRegistry.getFallbackImplementationMappings();
		for (Map.Entry<Class<?>, Class<?>> entry : fallbackImpls.entrySet()) {
			Class<?> serviceInterface = entry.getKey();
			Class<?> implementationClass = entry.getValue();

			try {
				binder.registerImplementation((Class<Object>) serviceInterface, (Class<Object>) implementationClass,
						"fallback", // 命名为 fallback
						false // 不设为主要实现
				);

				logger.debug("注册回退实现到DI: " + serviceInterface.getName() + " -> " + implementationClass.getName()
						+ " (fallback)");
			} catch (Exception e) {
				logger.error("注册回退实现失败: " + serviceInterface.getName() + " -> " + implementationClass.getName(), e);
			}
		}

		// 注册命名许可证实现
		Map<String, Class<?>> namedLicenseImpls = businessRegistry.getNamedLicenseImplementationMappings();
		for (Map.Entry<String, Class<?>> entry : namedLicenseImpls.entrySet()) {
			String key = entry.getKey();
			Class<?> implementationClass = entry.getValue();

			// 解析服务接口和名称
			String[] parts = key.split("#");
			if (parts.length == 2) {
				try {
					Class<?> serviceInterface = Class.forName(parts[0]);
					String serviceName = parts[1];

					binder.registerImplementation((Class<Object>) serviceInterface, (Class<Object>) implementationClass,
							serviceName, // 使用指定的服务名称
							true // 设为主要实现
					);

					logger.debug("注册命名许可证实现到DI: " + serviceInterface.getName() + " -> " + implementationClass.getName()
							+ " (name=" + serviceName + ")");
				} catch (Exception e) {
					logger.error("注册命名许可证实现失败: " + key, e);
				}
			}
		}

		// 注册命名回退实现
		Map<String, Class<?>> namedFallbackImpls = businessRegistry.getNamedFallbackImplementationMappings();
		for (Map.Entry<String, Class<?>> entry : namedFallbackImpls.entrySet()) {
			String key = entry.getKey();
			Class<?> implementationClass = entry.getValue();

			// 解析服务接口和名称
			String[] parts = key.split("#");
			if (parts.length == 2) {
				try {
					Class<?> serviceInterface = Class.forName(parts[0]);
					String serviceName = parts[1] + "_fallback";

					binder.registerImplementation((Class<Object>) serviceInterface, (Class<Object>) implementationClass,
							serviceName, // 使用指定的服务名称
							false // 不设为主要实现
					);

					logger.debug("注册命名回退实现到DI: " + serviceInterface.getName() + " -> " + implementationClass.getName()
							+ " (name=" + serviceName + ")");
				} catch (Exception e) {
					logger.error("注册命名回退实现失败: " + key, e);
				}
			}
		}
	}

	// ==================== PackageScanProviderChangeListener 实现
	// ====================

	/**
	 * 注册单个许可证服务
	 *
	 * @param binder             DI绑定器
	 * @param pluginId           插件ID
	 * @param interfaceName      接口名称
	 * @param implementationName 实现类名称
	 * @return 是否注册成功
	 */
	private boolean registerLicenseService(IBinder binder, String pluginId, String interfaceName,
			String implementationName) {
		try {
			// 首先尝试使用加密类加载器加载接口类
			Class<?> interfaceClass = loadInterfaceClass(pluginId, interfaceName);
			if (interfaceClass == null) {
				logger.debug("无法加载接口类，跳过动态注册: " + interfaceName + " (插件: " + pluginId + ")");
				return false;
			}

			// 尝试从许可证中获取实现
			Object licenseImplementation = licenseManager.createServiceInstanceFromLicense(interfaceClass);
			if (licenseImplementation != null) {
				logger.info("发现许可证实现，注册为主要实现: " + interfaceName + " -> " + licenseImplementation.getClass().getName()
						+ " (插件: " + pluginId + ")");

				// 注入许可证信息
				if (licenseImplementation instanceof LicenseAware) {
					injectLicenseInfo((LicenseAware) licenseImplementation, interfaceClass);
				}

				// 注册为单例，覆盖默认实现
				@SuppressWarnings("unchecked")
				Class<Object> typedInterfaceClass = (Class<Object>) interfaceClass;
				binder.bind(typedInterfaceClass, licenseImplementation);

				return true;
			} else {
				logger.debug("未找到许可证实现，将使用默认实现: " + interfaceName + " (插件: " + pluginId + ")");
				return false;
			}
		} catch (Exception e) {
			logger.warn("动态注册许可证服务失败: " + interfaceName + " (插件: " + pluginId + ")", e);
			return false;
		}
	}

	/**
	 * 动态注册许可证服务实现 从许可证文件中提取所有可用的服务映射并动态注册
	 */
	private void registerLicenseServices(IBinder binder) {
		logger.info("开始动态注册许可证服务...");

		try {
			// 获取所有已注册的插件ID
			java.util.List<String> pluginIds = licenseManager.getRegisteredPluginIds();
			if (pluginIds.isEmpty()) {
				logger.debug("未找到已注册的插件许可证，跳过许可证服务注册");
				return;
			}

			int totalRegisteredCount = 0;

			// 遍历所有插件许可证
			for (String pluginId : pluginIds) {
				Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
				if (!licenseOpt.isPresent()) {
					logger.debug("插件 " + pluginId + " 的许可证不存在，跳过");
					continue;
				}

				PluginLicense license = licenseOpt.get();
				if (!license.isValid()) {
					logger.debug("插件 " + pluginId + " 的许可证无效，跳过");
					continue;
				}

				if (!license.hasServiceMappings()) {
					logger.debug("插件 " + pluginId + " 的许可证中未包含服务映射，跳过");
					continue;
				}

				// 获取服务映射
				Map<String, String> serviceMappings = license.getServiceMappings();
				logger.info("插件 " + pluginId + " 发现 " + serviceMappings.size() + " 个许可证服务映射");

				// 遍历所有服务映射并注册
				int pluginRegisteredCount = 0;
				for (Map.Entry<String, String> entry : serviceMappings.entrySet()) {
					String interfaceName = entry.getKey();
					String implementationName = entry.getValue();

					if (registerLicenseService(binder, pluginId, interfaceName, implementationName)) {
						pluginRegisteredCount++;
						totalRegisteredCount++;
					}
				}

				logger.info("插件 " + pluginId + " 成功注册 " + pluginRegisteredCount + " 个许可证服务");
			}

			logger.info("许可证服务动态注册完成，总共成功注册 " + totalRegisteredCount + " 个服务");

		} catch (Exception e) {
			logger.error("动态注册许可证服务时发生异常", e);
		}
	}

	/**
	 * 测试类加载器是否能找到指定包中的类
	 */
	private void testClassLoading(String packagePath) {
		logger.info("测试类加载器对包 " + packagePath + " 的访问能力");

		String[] testClasses = { packagePath + ".IFeishuAuthenticatorEnhancer",
				packagePath + ".FeishuDefaultAuthenticatorEnhancer",
				packagePath + ".FeishuLicensedAuthenticatorEnhancer" };

		for (String className : testClasses) {
			try {
				Class<?> clazz = Class.forName(className);
				logger.info("✅ 成功加载类: " + className + " (类加载器: " + clazz.getClassLoader().getClass().getName() + ")");
			} catch (ClassNotFoundException e) {
				logger.warn("❌ 无法加载类: " + className);
			}
		}
	}

}